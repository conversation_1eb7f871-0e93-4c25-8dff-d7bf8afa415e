from typing import Any, Dict, Optional, Union

import discord
from discord.ext import commands

from auxiliary.services.story.story_logic import THEMES_BY_TITLE
from gacha.views.collection.collection_view.base_pagination import BasePaginationView

BotType = Union[commands.Bot, commands.AutoShardedBot]


def create_turn_embed(
    story_meta: Dict[str, Any],
    ai_turn: Dict[str, Any],
    user_turn: Optional[Dict[str, Any]],
    page: int,
    total_pages: int,
) -> discord.Embed:
    """根據回合數據創建一個標準的 Embed。"""
    description_text = f"**📖 故事情節**\n{ai_turn['content']}"
    if user_turn:
        description_text += f"\n\n**🤔 玩家的選擇**\n`{user_turn['content']}`"

    if len(description_text) > 4096:
        description_text = description_text[:4090] + "..."

    embed = discord.Embed(
        title=f"📖 {story_meta.get('title', '無標題')} - 分享故事",
        description=description_text,
        color=discord.Color.from_rgb(114, 137, 218),
    )

    # Display status block
    status_block_content = ai_turn.get("status_block")
    if status_block_content:
        embed.add_field(name="📊 角色狀態 📊", value=status_block_content, inline=False)

    # Set theme thumbnail
    theme_title = story_meta.get("theme_title")
    if theme_title:
        theme_data = THEMES_BY_TITLE.get(theme_title)
        if theme_data and theme_data.get("image_url"):
            embed.set_thumbnail(url=theme_data["image_url"])

    embed.set_footer(text=f"第 {page}/{total_pages} 頁 | 分享ID: {story_meta['id']}")
    return embed


class StoryReadOnlyView(BasePaginationView):
    """用於查看分享故事的只讀視圖"""

    def __init__(
        self,
        bot: BotType,
        user: Union[discord.User, discord.Member],
        story_data: Dict[str, Any],
    ):
        self.story_data = story_data
        self.turns = story_data["turns"]
        total_pages = (len(self.turns) + 1) // 2

        super().__init__(
            bot=bot,
            user_id=user.id,
            current_page=1,
            total_pages=total_pages,
            timeout=None,
        )

    async def _update_page(self, page: int, interaction: discord.Interaction):
        turn_index = (page - 1) * 2
        if turn_index >= len(self.turns):
            return

        ai_turn = self.turns[turn_index]
        user_turn = (
            self.turns[turn_index + 1] if turn_index + 1 < len(self.turns) else None
        )

        embed = create_turn_embed(
            story_meta=self.story_data["story"],
            ai_turn=ai_turn,
            user_turn=user_turn,
            page=page,
            total_pages=self.total_pages,
        )

        self._refresh_button_states()
        if not interaction.response.is_done():
            await interaction.response.edit_message(embed=embed, view=self)
        else:
            await interaction.edit_original_response(embed=embed, view=self)
