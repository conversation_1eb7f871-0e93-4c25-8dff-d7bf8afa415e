import asyncio
import logging
import random
from typing import Dict, Optional, Tuple

import yaml

logger = logging.getLogger(__name__)

_prompts: Dict = {}


async def load_prompts(
    prompts_file_path: str = "auxiliary/data/prompts/prompts/news_prompts.yaml",
):
    """
    從 YAML 檔案載入提示詞到模組級變數 _prompts（異步避免阻塞）。
    """
    global _prompts
    
    def _sync_load_yaml():
        try:
            with open(prompts_file_path, "r", encoding="utf-8") as f:
                prompts_data = yaml.safe_load(f)
                return prompts_data if prompts_data is not None else {}
        except FileNotFoundError:
            logger.error("找不到提示詞檔案：%s", prompts_file_path)
            raise
        except yaml.YAMLError as e:
            logger.error("解析 YAML 檔案失敗：%s - %s", prompts_file_path, e)
            raise
    
    try:
        _prompts = await asyncio.to_thread(_sync_load_yaml)
    except Exception:
        _prompts = {}
        raise

def load_prompts_sync(
    prompts_file_path: str = "auxiliary/data/prompts/prompts/news_prompts.yaml",
):
    """
    同步版本的載入函數，僅用於模組初始化時使用。
    """
    global _prompts
    try:
        with open(prompts_file_path, "r", encoding="utf-8") as f:
            prompts_data = yaml.safe_load(f)
            if prompts_data is None:
                _prompts = {}
            else:
                _prompts = prompts_data
    except FileNotFoundError:
        logger.error("找不到提示詞檔案：%s", prompts_file_path)
        _prompts = {}
        raise
    except yaml.YAMLError as e:
        logger.error("解析 YAML 檔案失敗：%s - %s", prompts_file_path, e)
        _prompts = {}
        raise


def get_prompt_templates(
    news_type: str, character_archetype: str
) -> Optional[Tuple[str, str]]:
    """
    根據新聞類型和角色原型獲取 system_prompt 和 user_prompt_template。
    """
    news_type_prompts = _prompts.get(news_type)
    if not news_type_prompts:
        logger.warning("在提示詞配置中找不到新聞類型 '%s'。", news_type)
        return None

    archetype_prompts = news_type_prompts.get(character_archetype)
    if not archetype_prompts:
        logger.warning(
            "在新聞類型 '%s' 下找不到角色原型 '%s' 的提示詞。",
            news_type,
            character_archetype,
        )
        return None

    system_prompt = archetype_prompts.get("system_prompt")

    user_prompts_list = archetype_prompts.get("user_prompts")
    user_prompt_template_single = archetype_prompts.get("user_prompt_template")

    selected_user_prompt = None

    if user_prompts_list and isinstance(user_prompts_list, list) and user_prompts_list:
        selected_user_prompt = random.choice(user_prompts_list)
    elif user_prompt_template_single and isinstance(user_prompt_template_single, str):
        selected_user_prompt = user_prompt_template_single

    if system_prompt is None or selected_user_prompt is None:
        missing_parts = []
        if system_prompt is None:
            missing_parts.append("system_prompt")
        if selected_user_prompt is None:
            missing_parts.append("user_prompts/user_prompt_template")

        logger.warning(
            "新聞類型 '%s' 下角色原型 '%s' 的提示詞結構不完整 (缺少 %s)。",
            news_type,
            character_archetype,
            ", ".join(missing_parts),
        )
        return None

    return system_prompt, selected_user_prompt


def get_formatted_prompt(
    news_type: str, character_archetype: str, data: Dict
) -> Optional[Tuple[str, str]]:
    """
    獲取格式化後的 system_prompt 和 user_prompt。
    """
    templates = get_prompt_templates(news_type, character_archetype)
    if not templates:
        return None

    system_prompt, user_prompt_template = templates

    try:
        # 確保 data 中的所有值都是字符串，以避免格式化錯誤
        safe_data = {k: str(v) for k, v in data.items()}
        formatted_system_prompt = system_prompt.format(**safe_data)
        formatted_user_prompt = user_prompt_template.format(**safe_data)
        return formatted_system_prompt, formatted_user_prompt
    except KeyError as e:
        logger.error(
            "格式化提示詞時缺少鍵：%s。新聞類型: %s, 角色: %s, 數據: %s",
            e,
            news_type,
            character_archetype,
            data,
        )
        return None
    except Exception as e:
        logger.error(
            "格式化提示詞時發生未知錯誤：%s。新聞類型: %s, 角色: %s",
            e,
            news_type,
            character_archetype,
        )
        return None


# 在模組加載時自動加載提示詞（使用同步版本）
try:
    load_prompts_sync()
except (FileNotFoundError, yaml.YAMLError):
    logger.critical("提示詞模組初始化失敗，AI相關功能可能無法正常工作。")
