# auxiliary/services/ai_core/http_client.py

import asyncio
import json
import logging
from enum import Enum
from typing import Any, Dict, Final, Optional

import aiohttp

logger = logging.getLogger(__name__)

# S級常數定義
MAX_CONCURRENT_REQUESTS: Final[int] = 100
MAX_PAYLOAD_SIZE: Final[int] = 50 * 1024 * 1024  # 50MB

class ErrorSeverity(Enum):
    """錯誤嚴重性級別"""
    LOW = "low"
    MEDIUM = "medium" 
    HIGH = "high"
    CRITICAL = "critical"

def _classify_error(exception: Exception) -> ErrorSeverity:
    """智能錯誤分類 - S級錯誤處理"""
    if isinstance(exception, aiohttp.ClientResponseError):
        return ErrorSeverity.HIGH if 400 <= exception.status < 500 else ErrorSeverity.MEDIUM
    elif isinstance(exception, asyncio.TimeoutError):
        return ErrorSeverity.MEDIUM
    elif isinstance(exception, aiohttp.ClientError):
        return ErrorSeverity.HIGH
    else:
        return ErrorSeverity.CRITICAL

# 全域的 aiohttp.ClientSession，在應用程式啟動時初始化
# 這可以重用 TCP 連接，大幅提升性能
_session: Optional[aiohttp.ClientSession] = None
# 並發安全：使用鎖保護 session 初始化
_session_lock = asyncio.Lock()
# 高併發：使用信號量控制同時請求數量
_request_semaphore = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)


async def initialize_session():
    """在應用程式啟動時初始化全域 aiohttp Session。"""
    global _session
    async with _session_lock:  # 並發安全修正
        if _session is None or _session.closed:
            # 設定合理的超時和連接池大小
            # 針對 AI API 的超時配置
            timeout = aiohttp.ClientTimeout(
                total=180.0,  # 總超時 180 秒（AI 回應時間）
                connect=10.0,  # 連接超時 10 秒
                sock_read=180.0,  # 讀取超時 180 秒（等待 AI 回應）
                sock_connect=10.0,  # Socket 連接超時 10 秒
            )
            connector = aiohttp.TCPConnector(
                limit_per_host=200,  # 高併發：每個主機更多連接
                limit=500,  # 高併發：總連接池大小
                ttl_dns_cache=300,  # DNS 快取 5 分鐘
                use_dns_cache=True,
                enable_cleanup_closed=True,  # 啟用清理已關閉的連接
                keepalive_timeout=300,  # Keep-alive 超時：5分鐘，確保長時間 AI 請求不會斷開
            )
            _session = aiohttp.ClientSession(timeout=timeout, connector=connector)
            logger.info("全域 aiohttp.ClientSession 已初始化。")


async def close_session():
    """在應用程式關閉時關閉全域 aiohttp Session。"""
    global _session
    async with _session_lock:  # 並發安全修正
        if _session and not _session.closed:
            await _session.close()
            _session = None
            logger.info("全域 aiohttp.ClientSession 已關閉。")


async def post_request(
    url: str, headers: Dict[str, str], payload: Dict[str, Any], stream: bool = False
) -> aiohttp.ClientResponse:
    """
    執行一個底層的 aiohttp POST 請求。

    Args:
        url (str): 目標 API 端點。
        headers (Dict[str, str]): HTTP 請求標頭。
        payload (Dict[str, Any]): 要發送的 JSON 數據。
        stream (bool): 是否使用流式傳輸。

    Returns:
        aiohttp.ClientResponse: 伺服器的回應物件。

    Raises:
        aiohttp.ClientError: 當網路請求失敗時。
    """
    # 輸入驗證
    if not isinstance(url, str) or not url.strip():
        raise ValueError("URL 不能為空")
    if not isinstance(headers, dict):
        raise TypeError("headers 必須是字典")
    if not isinstance(payload, dict):
        raise TypeError("payload 必須是字典")

    # 安全檢查：URL 格式驗證
    if not (url.startswith("http://") or url.startswith("https://")):
        raise ValueError("URL 必須以 http:// 或 https:// 開頭")

    # S級安全檢查：防止請求過大的 payload（異步處理避免阻塞）
    async def _check_payload_size():
        try:
            payload_json = await asyncio.to_thread(json.dumps, payload)
            payload_size = len(payload_json.encode("utf-8"))
            if payload_size > MAX_PAYLOAD_SIZE:
                raise ValueError(
                    f"Payload 過大: {payload_size:,} bytes > {MAX_PAYLOAD_SIZE:,} bytes"
                )
        except (TypeError, ValueError) as e:
            if "Payload 過大" in str(e):
                raise
            raise ValueError("Payload 包含不可序列化的數據") from e
    
    await _check_payload_size()

    if _session is None or _session.closed:
        logger.warning("Session 未初始化，正在臨時建立。建議在應用啟動時初始化。")
        await initialize_session()

    # 安全修正：創建 payload 副本，避免修改原始參數
    request_payload = payload.copy()
    request_payload["stream"] = stream

    response = None
    # 高併發：使用信號量控制併發請求
    async with _request_semaphore:
        try:
            # 直接使用全域 session 發送請求
            assert _session is not None, "Session not initialized"
            response = await _session.post(url, headers=headers, json=request_payload)

            # 檢查回應狀態碼，如果不成功則立即拋出異常
            response.raise_for_status()

            return response

        except Exception as e:
            # 統一處理所有異常，確保 response 被關閉
            # 注意：在 aiohttp 中，最佳實踐是讓 session 自動管理 response 生命週期
            # 或者使用 context manager，這裡我們讓 session 自動處理
            # 因為強制關閉可能會干擾 session 的連接池管理
            
            # 安全處理 URL，防止 API Key 洩露
            safe_url = url.split("?")[0] if "?" in url else url
            
            # 根據異常類型記錄不同級別的日誌
            if isinstance(e, aiohttp.ClientResponseError):
                logger.error(f"API 請求返回 HTTP 錯誤: {e.status} {e.message} for {safe_url}")
            elif isinstance(e, asyncio.TimeoutError):
                logger.error(f"API 請求超時: {safe_url}")
            elif isinstance(e, aiohttp.ClientError):
                logger.error(f"API 請求發生網路客戶端錯誤: {e} for {safe_url}")
            else:
                logger.error(f"API 請求發生未預期錯誤: {e} for {safe_url}")
            
            raise
