"""
處理 AI 劇情生成的核心邏輯，包括與 ai_service 的溝通和透過 repository 的資料庫互動。
此模組現在是無類別的，並提供模組級的異步函數。
"""

import logging
import re
import uuid
from typing import Any, Dict, List, Optional

from auxiliary.repositories import story_repository
from auxiliary.repositories.story_repository import Story, StoryTurn
from database.postgresql.async_manager import get_pool

from ..ai_core import ai_service
from . import prompts
from .cleanup_strategies import apply_cleanup_strategy, get_cleanup_strategy
from .constants import (
    DEFAULT_LIMITED_TURNS,
    DEFAULT_MAX_LONG_TERM_SUMMARY_TURNS,
    DEFAULT_MAX_SHORT_TERM_TURNS,
    StoryStatus,
    TurnRole,
)
from .themes import STORY_THEMES

# 初始化 logger
logger = logging.getLogger(__name__)

# 在模組加載時快取主題
THEMES_BY_TITLE = {theme["title"]: theme for theme in STORY_THEMES}

# --- Private Helper Functions ---


def _sanitize_user_input(text: str) -> str:
    """全面清理用戶輸入，防止各種注入攻擊。"""
    import html

    if not text or not isinstance(text, str):
        return ""

    # HTML轉義，防止XSS
    text = html.escape(text)

    # 移除潛在的控制字符
    text = re.sub(r"[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]", "", text)

    # 限制長度，防止DoS攻擊
    max_length = 10000
    if len(text) > max_length:
        logger.warning(
            "用戶輸入過長，已截斷從 %d 字符到 %d 字符", len(text), max_length
        )
        text = text[:max_length]

    # 移除過多的連續空白符
    text = re.sub(r"\s{10,}", " " * 5, text)

    return text.strip()


def _cleanup_ai_response(raw_text: str, library_name: str = "default") -> str:
    """清理 AI 回應，移除不必要的標籤和內容。

    Args:
        raw_text: 原始AI回复文本
        library_name: 提示词库名称，不同库有不同的清理策略
    """
    # 使用新的清理策略系统进行前端清理
    strategy = get_cleanup_strategy(library_name)
    return apply_cleanup_strategy(raw_text, strategy, cleanup_type="frontend")


async def _generate_and_parse_story(
    messages: List[Dict[str, str]], library_name: str = "default"
) -> Optional[Dict[str, Any]]:
    """向 AI 服務發送請求並解析回應。"""
    try:
        # 驗證 messages
        if not messages:
            raise ValueError("No messages provided")
        
        # 【【【核心改動】】】
        # 直接傳遞完整的 messages 列表！
        response = await ai_service.process_messages(messages=messages)
        
        raw_text = ai_service._get_raw_content_from_response(response)
        if not raw_text:
            raw_text = ai_service.extract_response_text(response, require_ai_payload=False)

        if not raw_text:
            logger.warning(
                "AI service returned empty or invalid content. Returning None to trigger regeneration."
            )
            return None

        # --- 順序調整開始 ---

        # 1. 優先清理 <thinking> 標籤，避免干擾後續解析
        clean_raw_text = re.sub(r"<thinking>[\s\S]*?</thinking>\s*", "", raw_text, flags=re.IGNORECASE)

        # 2. 從清理後的文本中解析所有需要的區塊

        status_block_match = re.search(
            r"<status_block>([\s\S]*?)</status_block>", clean_raw_text, re.DOTALL
        )
        status_block_content = (
            status_block_match.group(1).strip() if status_block_match else None
        )

        options_match = re.search(r"<options>(.*?)</options>", clean_raw_text, re.DOTALL)
        options = []
        if options_match:
            options_block = options_match.group(1)
            option_matches = re.findall(r"<option_\d>(.*?)</option_\d>", options_block)
            if option_matches:
                options = [opt.strip() for opt in option_matches]

        # 先檢查舊格式: <details><summary>摘要</summary>內容</details> (向下兼容)
        summary_match_old = re.search(
            r"<details>\s*<summary>摘要</summary>\s*([\s\S]*?)</details>",
            clean_raw_text,
            re.IGNORECASE,
        )
        
        # 如果沒有舊格式，再檢查新格式: <summary>內容</summary> (簡化格式)
        summary_match_new = None
        if not summary_match_old:
            # 尋找獨立的 summary 標籤（不在 details 內）
            summary_match_new = re.search(
                r"<summary>\s*([\s\S]*?)</summary>",
                clean_raw_text,
                re.IGNORECASE,
            )
        
        # 優先使用舊格式（如果存在），否則使用新格式
        if summary_match_old:
            summary_content = summary_match_old.group(0).strip()
        elif summary_match_new:
            summary_content = f"<summary>{summary_match_new.group(1).strip()}</summary>"
        else:
            summary_content = None

        # MODIFIED: 直接從原始文本中解析 <content>
        story_match = re.search(r"<content>(.*?)</content>", clean_raw_text, re.DOTALL)

        # 如果找到了<content>標籤，就用它的內容，否則用整個清理後的文本作為備用
        if story_match:
            story_content_raw = story_match.group(1).strip()
        else:
            # 如果沒有 <content>，我們需要一個備案：清理整個文本，剩下的就是內容
            story_content_raw = clean_raw_text

        # 2. 現在，只對提取出的故事內容進行清理，用於前端顯示
        # 這樣做不會影響我們對其他標籤的解析
        cleaned_content = _cleanup_ai_response(story_content_raw, library_name)

        if not cleaned_content:
            # 如果清理後內容為空，說明AI可能只返回了<thinking>等標籤，沒有實際劇情
            logger.warning(
                "AI response contained no displayable content after cleanup. Raw content was: %s",
                story_content_raw[:500],
            )
            return None  # 觸發重新生成

        # --- 順序調整結束 ---

        return {
            "content": cleaned_content,  # 返回清理後的內容
            "options": options[:4],
            "summary": summary_content,
            "status_block": status_block_content,
        }

    except Exception as e:
        from auxiliary.exceptions import AIConnectionError

        raise AIConnectionError(
            "Failed to generate or parse story from AI service."
        ) from e


# --- Public Logic Functions ---


async def start_new_story(
    user_id: int, user_display_name: str, theme_title: str
) -> Optional[Dict[str, Any]]:
    """開始一個新故事。"""
    theme = THEMES_BY_TITLE.get(theme_title)
    if not theme:
        logger.error("找不到主題: %s", theme_title)
        return None

    story_id = uuid.uuid4()
    opening_line_template = theme.get("opening_line", "故事開始了...")
    opening_line = opening_line_template.format(user_display_name=user_display_name)

    pool = get_pool()
    if not pool:
        raise RuntimeError("Database pool is not available")

    async with pool.acquire() as conn:
        async with conn.transaction():
            # 1. 暫停用戶其他活躍的故事
            await story_repository.pause_user_active_stories(user_id, conn=conn)

            # 2. 創建新的故事元數據記錄
            story_meta = Story(
                id=story_id,
                user_id=user_id,
                title=theme_title,
                theme_title=theme_title,
                status=StoryStatus.ACTIVE.value,
                is_public=False,
            )
            await story_repository.create_story(story_meta, conn=conn)

            # 3. 創建故事的開幕詞 (第一個回合)
            status_block_template = theme.get("initial_status_block")
            final_status_block = (
                status_block_template.format(user_display_name=user_display_name)
                if status_block_template
                else None
            )

            turn_data = StoryTurn(
                story_id=story_id,
                turn_number=1,
                role=TurnRole.ASSISTANT.value,
                content=opening_line,
                options=theme["initial_options"],
                status_block=final_status_block,
            )
            await story_repository.add_story_turn(turn_data, conn=conn)

    return {
        "story_id": story_id,
        "content": opening_line,
        "options": theme["initial_options"],
    }


async def continue_story(
    user_id: int,
    user_display_name: str,
    story_id: uuid.UUID,
    user_choice: str,
) -> Optional[Dict[str, Any]]:
    """繼續一個現有的故事。"""
    sanitized_choice = _sanitize_user_input(user_choice)

    # 1. 高效獲取故事數據（只取最近回合，足夠支援長短期記憶）
    limited_story_data = await story_repository.get_story_with_limited_turns(
        user_id, story_id, max_turns=DEFAULT_LIMITED_TURNS
    )
    if not limited_story_data:
        logger.warning("試圖繼續一個不存在的故事 (ID: %s, user_id: %s)。可能原因：1.故事不存在 2.用戶ID不匹配 3.資料庫查詢失敗", story_id, user_id)
        return None
    
    if limited_story_data["story"]["status"] != StoryStatus.ACTIVE.value:
        logger.warning("試圖繼續一個非活躍狀態的故事 (ID: %s, user_id: %s, 當前狀態: %s)", story_id, user_id, limited_story_data["story"]["status"])
        return None

    story_meta = limited_story_data["story"]
    theme_title = story_meta["theme_title"]
    theme_settings = THEMES_BY_TITLE.get(theme_title)
    if not theme_settings:
        logger.error("找不到故事 %s 的主題: %s", story_id, theme_title)
        return None

    all_turns = limited_story_data["turns"]

    # 2. 準備短期歷史 (最近回合，包含狀態欄等所有資訊)
    short_term_history = all_turns[-DEFAULT_MAX_SHORT_TERM_TURNS:]

    # 3. 準備長期記憶 (最近回合的摘要)
    # 新增邏輯：只有在總回合數超過短期記憶門檻時才發送摘要
    long_term_summaries = []  # 預設為空列表
    if len(all_turns) > DEFAULT_MAX_SHORT_TERM_TURNS:
        # 修正：長期摘要應該排除短期記憶中的回合，避免重複
        # 取得短期記憶之前的回合作為長期摘要來源
        long_term_end_index = len(all_turns) - DEFAULT_MAX_SHORT_TERM_TURNS
        long_term_start_index = max(0, long_term_end_index - DEFAULT_MAX_LONG_TERM_SUMMARY_TURNS)
        summary_source_turns = all_turns[long_term_start_index:long_term_end_index]
        long_term_summaries = [
            turn["summary"] for turn in summary_source_turns 
            if turn.get("summary") and turn["summary"].strip()
        ]

    # 4. 建立提示詞訊息
    # 获取当前主题使用的提示词库名称
    library_name = theme_settings.get("prompt_library", "default")
    messages = prompts.build_prompt_messages(
        theme_settings=theme_settings,
        long_term_summaries=long_term_summaries,
        short_term_history=short_term_history,
        user_input=sanitized_choice,
        user_display_name=user_display_name,
        library_name=library_name,
    )

    ai_response = await _generate_and_parse_story(messages, library_name)
    if not ai_response:
        return None

    pool = get_pool()
    if not pool:
        raise RuntimeError("Database pool is not available")

    async with pool.acquire() as conn:
        async with conn.transaction():
            # 再次檢查故事狀態，防止在 AI 生成期間被改變（高效方法）
            current_status = await story_repository.get_story_status(
                user_id, story_id, conn=conn
            )
            if current_status != StoryStatus.ACTIVE.value:
                logger.warning(
                    f"故事 {story_id} 在生成期間狀態已改變，已中止寫入新回合。"
                )
                return None

            user_turn_number = await story_repository.get_next_turn_number(
                story_id, conn=conn
            )
            assistant_turn_number = user_turn_number + 1

            user_turn = StoryTurn(
                story_id=story_id,
                turn_number=user_turn_number,
                role=TurnRole.USER.value,
                content=sanitized_choice,
            )
            await story_repository.add_story_turn(user_turn, conn=conn)

            assistant_turn = StoryTurn(
                story_id=story_id,
                turn_number=assistant_turn_number,
                role=TurnRole.ASSISTANT.value,
                content=ai_response["content"],
                options=ai_response["options"],
                summary=ai_response.get("summary"),
                status_block=ai_response.get("status_block"),
            )
            await story_repository.add_story_turn(assistant_turn, conn=conn)

    return {"story_id": story_id, **ai_response}


async def revert_story_to_turn(user_id: int, story_id: uuid.UUID, turn_number: int):
    """回溯故事到指定的回合。"""
    # 驗證該用戶是否擁有此故事（高效驗證，不需要加載所有數據）
    if not await story_repository.verify_story_ownership(user_id, story_id):
        logger.warning("用戶 %s 試圖回溯不屬於他們的故事 %s", user_id, story_id)
        return
    await story_repository.revert_story_to_turn(story_id, turn_number)


async def get_user_history(user_id: int) -> Optional[Dict[str, Any]]:
    """獲取用戶活躍故事的摘要（元數據和總回合數）。"""
    summary = await story_repository.get_active_story_summary(user_id)
    if not summary:
        return None

    # 為了與 StoryMasterView 的期望格式保持一致，將其包裝一下
    return {
        "story": {
            "id": summary["id"],
            "title": summary["title"],
            "theme_title": summary["theme_title"],
            "status": summary["status"],
            "is_public": summary["is_public"],
        },
        "total_turn_count": summary["total_turn_count"],
        "turns": [],  # 保持鍵存在，但初始為空
    }


async def get_story_turns_for_page(
    story_id: uuid.UUID, page: int
) -> List[Dict[str, Any]]:
    """從 repository 獲取特定頁面的回合數據。"""
    return await story_repository.get_story_turns_by_page(story_id, page)


async def clear_history(user_id: int):
    """清除用戶的活躍故事（將其狀態設為 paused）。"""
    await story_repository.pause_user_active_stories(user_id)


async def get_user_stories(user_id: int) -> List[Dict[str, Any]]:
    """獲取用戶的所有故事列表。"""
    return await story_repository.get_user_stories(user_id)


async def resume_story(user_id: int, story_id: uuid.UUID) -> bool:
    """恢復一個故事為活躍狀態。"""
    pool = get_pool()
    if not pool:
        raise RuntimeError("Database pool is not available")
    async with pool.acquire() as conn:
        async with conn.transaction():
            # 先暫停所有，再激活指定的
            return await story_repository.resume_story(user_id, story_id, conn=conn)


async def get_story_by_id(story_id: uuid.UUID) -> Optional[Dict[str, Any]]:
    """根據 ID 獲取一個公開的故事。"""
    return await story_repository.get_story_by_id_if_public(story_id)


async def toggle_story_public(user_id: int, story_id: uuid.UUID) -> Optional[bool]:
    """切換故事的公開狀態。"""
    return await story_repository.toggle_story_public(user_id, story_id)


async def delete_story(user_id: int, story_id: uuid.UUID) -> bool:
    """刪除一個故事。"""
    return await story_repository.delete_story(user_id, story_id)


async def rename_story(user_id: int, story_id: uuid.UUID, new_title: str) -> bool:
    """重新命名一個故事。"""
    return await story_repository.update_story_title(user_id, story_id, new_title)


async def regenerate_last_turn(
    user_id: int, story_id: uuid.UUID, user_display_name: str
) -> Optional[Dict[str, Any]]:
    """重新生成故事的最後一個 AI 回應。

    Args:
        user_id: 用戶ID
        story_id: 故事ID
        user_display_name: 用戶顯示名稱，用於正確的提示詞生成
    """
    # 1. 高效獲取故事數據（只需要最近一些回合來找到最後的user turn）
    limited_story_data = await story_repository.get_story_with_limited_turns(
        user_id,
        story_id,
        max_turns=20,  # 只需要最近20回合來找到最後的user turn
    )
    if not limited_story_data:
        logger.warning("試圖在不存在的故事上重新生成 (ID: %s, user_id: %s)。可能原因：1.故事不存在 2.用戶ID不匹配 3.資料庫查詢失敗", story_id, user_id)
        return None
    
    if limited_story_data["story"]["status"] != StoryStatus.ACTIVE.value:
        logger.warning("試圖在非活躍狀態的故事上重新生成 (ID: %s, user_id: %s, 當前狀態: %s)", story_id, user_id, limited_story_data["story"]["status"])
        return None

    all_turns = limited_story_data["turns"]

    # 至少需要一個 assistant turn 和一個 user turn 才能重新生成
    if len(all_turns) < 2:
        logger.warning("故事 %s 的回合數不足，無法重新生成。", story_id)
        return None

    # 2. 獲取最後的使用者選擇
    last_user_turn = None
    for turn in reversed(all_turns):
        if turn["role"] == TurnRole.USER.value:
            last_user_turn = turn
            break

    if not last_user_turn:
        logger.warning("在故事 %s 中找不到最後的使用者回合，無法重新生成。", story_id)
        return None

    user_choice = last_user_turn["content"]
    user_turn_number = last_user_turn["turn_number"]

    # 3. 回溯到使用者選擇之前的狀態
    # 我們要刪除 user turn 和之後的 assistant turn，所以回溯到 user_turn_number - 1
    await story_repository.revert_story_to_turn(story_id, user_turn_number - 1)

    # 4. 重新執行 continue_story 邏輯
    # continue_story 會處理歷史記錄的建立、AI 呼叫和資料庫儲存
    # 因為我們已經回溯了，所以它可以安全地重新添加 user turn 和新的 assistant turn
    return await continue_story(user_id, user_display_name, story_id, user_choice)
