"""
故事系統相關常數定義
"""

from enum import Enum


class StoryStatus(Enum):
    """故事狀態枚舉"""
    ACTIVE = "active"
    PAUSED = "paused"


class TurnRole(Enum):
    """回合角色枚舉"""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class UIComponentID:
    """UI組件自定義ID常數"""
    STORY_SELECT_MENU = "story_select_menu"
    CUSTOM_RESPONSE = "custom_response"
    REVERT_AND_CONTINUE = "revert_and_continue"
    SHARE_STORY = "share_story"
    RETRY_GENERATION = "retry_generation"
    REGENERATE_STORY = "regenerate_story"
    SUB_PREV = "sub_prev"
    SUB_NEXT = "sub_next"


# 文本長度限制常數
EMBED_DESCRIPTION_LIMIT = 2000
EMBED_FIELD_VALUE_LIMIT = 1024

# 故事生成參數
DEFAULT_MAX_SHORT_TERM_TURNS = 20
DEFAULT_MAX_LONG_TERM_SUMMARY_TURNS = 200
DEFAULT_LIMITED_TURNS = 220  # 足夠支援短期記憶(20)+長期摘要(200)